# Document Intelligence Agent - N8N Implementation Guide

## Agent Overview

The Document Intelligence Agent is the first and foundational agent in the Agentic Order Capture Workflow. It serves as the entry point for all incoming purchase order communications and is responsible for intelligent email classification, document processing, and data extraction.

### Agent Architecture
- **Primary Agent**: Document Intelligence Agent
- **Sub-Agents**:
  - Email Classifier Sub-Agent
  - Data Extractor Sub-Agent

### Business Value
- **Automation Rate**: 78% of emails processed without human intervention
- **Processing Time Reduction**: 35% faster than manual processing
- **Accuracy Improvement**: 94% classification accuracy
- **Cost Savings**: $2,850/week in labor cost reduction

## Implementation Requirements

### Prerequisites
- N8N instance with AI/LLM capabilities
- Email integration (Gmail/Outlook)
- Database for storing processed data
- File storage for attachments
- LLM API access (OpenAI, Claude, or similar)

### Required N8N Nodes
1. **Email Trigger Node** (Gmail/Outlook)
2. **AI Agent Node** (OpenAI/Claude)
3. **Code Node** (JavaScript)
4. **HTTP Request Node**
5. **Set Node**
6. **IF Node**
7. **Database Node** (PostgreSQL/MySQL)
8. **File Storage Node**
9. **Error Handling Nodes**

## Phase 1: Email Detection & Classification

### Node 1: Email Monitor Setup

**Node Type**: Gmail Trigger / Outlook Trigger
**Purpose**: Monitor incoming emails for PO-related content

#### Configuration:
```json
{
  "pollTimes": {
    "item": [
      {
        "mode": "everyMinute",
        "minute": 5
      }
    ]
  },
  "filters": {
    "subject": "PO|Purchase Order|Order|Amendment",
    "from": "@techcorp.com|@globalmanufacturing.com|@retailchain.com"
  },
  "options": {
    "downloadAttachments": true,
    "markAsRead": false,
    "includeAttachments": true
  }
}
```

#### Output Data Structure:
```json
{
  "messageId": "string",
  "sender": "string",
  "senderName": "string",
  "recipient": "string",
  "subject": "string",
  "receivedDateTime": "ISO 8601",
  "body": "string",
  "attachments": [
    {
      "filename": "string",
      "size": "number",
      "contentType": "string",
      "data": "base64"
    }
  ],
  "priority": "string"
}
```

### Node 2: Email Classification Agent

**Node Type**: AI Agent (OpenAI/Claude)
**Purpose**: Classify email type and extract initial context

#### LLM Configuration:
```json
{
  "model": "gpt-4-turbo",
  "temperature": 0.1,
  "maxTokens": 2000,
  "systemPrompt": "You are an expert email classifier for purchase order processing. Analyze emails and classify them accurately with high confidence scores."
}
```

#### Prompt Template:
```
Analyze the following email and classify it according to these categories:

EMAIL TYPES:
- PURCHASE_ORDER: New purchase order submission
- PO_AMENDMENT: Changes to existing purchase order
- PRICE_INQUIRY: Request for pricing information
- ORDER_CANCELLATION: Request to cancel existing order
- GENERAL_INQUIRY: General business inquiry

URGENCY LEVELS:
- HIGH: Urgent delivery, time-sensitive
- MEDIUM: Standard business priority
- LOW: Non-urgent inquiry

EMAIL DATA:
Subject: {{$json.subject}}
From: {{$json.sender}} ({{$json.senderName}})
Body: {{$json.body}}
Attachments: {{$json.attachments.length}} files
Priority: {{$json.priority}}

ANALYSIS REQUIREMENTS:
1. Classify email type with confidence score (0-1)
2. Determine urgency level
3. Extract key information (PO numbers, customer details, special requirements)
4. Perform sentiment analysis
5. Assess relationship context
6. Calculate priority score (1-10)

Return response in this exact JSON format:
{
  "messageId": "{{$json.messageId}}",
  "classification": {
    "emailType": "CATEGORY",
    "confidence": 0.XX,
    "urgencyLevel": "LEVEL",
    "hasAttachments": boolean,
    "attachmentTypes": ["TYPE1", "TYPE2"],
    "requiredAction": "ACTION",
    "extractedInfo": {
      "poNumber": "string or null",
      "customerCompany": "string",
      "deliveryRequirement": "string or null",
      "specialInstructions": "string or null"
    },
    "sentimentAnalysis": {
      "overallSentiment": "SENTIMENT",
      "urgencyIndicators": ["indicator1", "indicator2"],
      "satisfactionLevel": "LEVEL",
      "emotionalTone": "TONE"
    },
    "relationshipContext": {
      "customerType": "NEW_CUSTOMER|RETURNING_CUSTOMER",
      "relationshipLength": "string",
      "previousInteractions": number,
      "escalationHistory": "NONE|LOW|MEDIUM|HIGH"
    },
    "priorityScoring": {
      "customerValue": "LOW|MEDIUM|HIGH",
      "orderSizeIndicator": "SMALL|MEDIUM|LARGE",
      "strategicImportance": "LOW|MEDIUM|HIGH|CRITICAL",
      "overallPriority": number,
      "priorityFactors": ["factor1", "factor2"]
    }
  },
  "nextAction": "ACTION_TYPE",
  "processingPriority": number
}
```

### Node 3: Classification Router

**Node Type**: IF Node
**Purpose**: Route emails based on classification results

#### Routing Logic:
```javascript
// Route based on email type and confidence
const classification = $json.classification;
const confidence = classification.confidence;
const emailType = classification.emailType;

// High confidence routing
if (confidence >= 0.9) {
  if (emailType === 'PURCHASE_ORDER') {
    return [0]; // Route to PO processing
  } else if (emailType === 'PO_AMENDMENT') {
    return [1]; // Route to amendment processing
  } else if (emailType === 'PRICE_INQUIRY') {
    return [2]; // Route to sales team
  }
}

// Low confidence - route to human review
if (confidence < 0.7) {
  return [3]; // Route to exception queue
}

// Medium confidence - additional validation
return [4]; // Route to secondary validation
```

### Node 4: Data Storage

**Node Type**: Database Node (PostgreSQL)
**Purpose**: Store classification results and email metadata

#### Database Schema:
```sql
CREATE TABLE email_classifications (
  id SERIAL PRIMARY KEY,
  message_id VARCHAR(255) UNIQUE,
  sender_email VARCHAR(255),
  sender_name VARCHAR(255),
  subject TEXT,
  received_datetime TIMESTAMP,
  email_type VARCHAR(50),
  confidence_score DECIMAL(3,2),
  urgency_level VARCHAR(20),
  priority_score INTEGER,
  extracted_info JSONB,
  sentiment_analysis JSONB,
  relationship_context JSONB,
  priority_scoring JSONB,
  next_action VARCHAR(100),
  processing_priority INTEGER,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Insert Query:
```sql
INSERT INTO email_classifications (
  message_id, sender_email, sender_name, subject, received_datetime,
  email_type, confidence_score, urgency_level, priority_score,
  extracted_info, sentiment_analysis, relationship_context,
  priority_scoring, next_action, processing_priority
) VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
);
```

## Phase 2: Attachment Processing

### Node 5: Attachment Handler

**Node Type**: Code Node (JavaScript)
**Purpose**: Process and validate email attachments

#### JavaScript Code:
```javascript
// Process attachments from classified emails
const emailData = $json;
const attachments = emailData.attachments || [];
const processedAttachments = [];

for (const attachment of attachments) {
  const fileInfo = {
    filename: attachment.filename,
    size: attachment.size,
    contentType: attachment.contentType,
    isProcessable: false,
    extractionMethod: null
  };

  // Determine if attachment is processable
  if (attachment.contentType === 'application/pdf') {
    fileInfo.isProcessable = true;
    fileInfo.extractionMethod = 'PDF_EXTRACTION';
  } else if (attachment.contentType.includes('spreadsheet') ||
             attachment.contentType.includes('excel')) {
    fileInfo.isProcessable = true;
    fileInfo.extractionMethod = 'EXCEL_EXTRACTION';
  } else if (attachment.contentType.includes('word')) {
    fileInfo.isProcessable = true;
    fileInfo.extractionMethod = 'WORD_EXTRACTION';
  }

  processedAttachments.push({
    ...fileInfo,
    data: attachment.data
  });
}

return {
  messageId: emailData.messageId,
  classification: emailData.classification,
  attachments: processedAttachments,
  processingRequired: processedAttachments.some(att => att.isProcessable)
};
```

## Error Handling & Quality Assurance

### Node 6: Error Handler

**Node Type**: Error Trigger
**Purpose**: Handle processing errors and route to appropriate resolution

#### Error Handling Logic:
```javascript
const error = $json.error;
const originalData = $json.originalData;

const errorResponse = {
  errorId: `ERR-${Date.now()}`,
  timestamp: new Date().toISOString(),
  errorType: error.name,
  errorMessage: error.message,
  originalMessageId: originalData?.messageId,
  severity: 'MEDIUM',
  requiresHumanIntervention: false,
  retryable: true,
  retryCount: 0,
  maxRetries: 3
};

// Determine error severity and handling
if (error.name === 'LLM_API_ERROR') {
  errorResponse.severity = 'HIGH';
  errorResponse.retryable = true;
  errorResponse.requiresHumanIntervention = false;
} else if (error.name === 'DATABASE_ERROR') {
  errorResponse.severity = 'CRITICAL';
  errorResponse.retryable = true;
  errorResponse.requiresHumanIntervention = true;
} else if (error.name === 'ATTACHMENT_PROCESSING_ERROR') {
  errorResponse.severity = 'MEDIUM';
  errorResponse.retryable = false;
  errorResponse.requiresHumanIntervention = true;
}

return errorResponse;
```

## Testing Procedures

### Test Case 1: Standard PO Email
**Input**: Email with clear PO subject and PDF attachment
**Expected Output**:
- Classification: PURCHASE_ORDER
- Confidence: >0.9
- Next Action: EXTRACT_PO_DATA

### Test Case 2: Amendment Email
**Input**: Email with "AMENDMENT" in subject
**Expected Output**:
- Classification: PO_AMENDMENT
- Confidence: >0.85
- Next Action: PROCESS_AMENDMENT

### Test Case 3: Ambiguous Email
**Input**: Email with unclear intent
**Expected Output**:
- Classification: Any type
- Confidence: <0.7
- Route to human review

### Test Case 4: Error Scenarios
**Input**: Malformed email data
**Expected Output**:
- Error handling triggered
- Appropriate error classification
- Retry logic activated

## Performance Monitoring

### Key Metrics to Track:
1. **Classification Accuracy**: Target >94%
2. **Processing Time**: Target <2 minutes per email
3. **Confidence Scores**: Monitor distribution
4. **Error Rates**: Target <5%
5. **Human Intervention Rate**: Target <22%

### Monitoring Queries:
```sql
-- Daily classification accuracy
SELECT
  DATE(created_at) as date,
  email_type,
  AVG(confidence_score) as avg_confidence,
  COUNT(*) as total_processed
FROM email_classifications
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(created_at), email_type;

-- Error rate monitoring
SELECT
  DATE(created_at) as date,
  COUNT(CASE WHEN confidence_score < 0.7 THEN 1 END) as low_confidence,
  COUNT(*) as total,
  ROUND(COUNT(CASE WHEN confidence_score < 0.7 THEN 1 END) * 100.0 / COUNT(*), 2) as error_rate
FROM email_classifications
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(created_at);
```

## Implementation Checklist

### Pre-Implementation:
- [ ] N8N instance configured with required nodes
- [ ] LLM API credentials configured
- [ ] Email integration tested
- [ ] Database schema created
- [ ] File storage configured

### Implementation Steps:
- [ ] Create email trigger workflow
- [ ] Configure AI classification node
- [ ] Set up routing logic
- [ ] Implement data storage
- [ ] Configure error handling
- [ ] Set up monitoring
- [ ] Test with sample data
- [ ] Deploy to production
- [ ] Monitor performance metrics

### Post-Implementation:
- [ ] Performance monitoring dashboard
- [ ] Regular accuracy reviews
- [ ] Continuous improvement based on patterns
- [ ] Training data updates
- [ ] Documentation updates

## Next Steps

After successful implementation of the Document Intelligence Agent, proceed to:
1. **Data Extractor Sub-Agent** - Extract structured data from PO documents
2. **Data Quality Agent** - Validate and enrich extracted data
3. **Business Rules Agent** - Apply pricing and business logic validation

This agent serves as the foundation for the entire agentic order capture workflow and must be thoroughly tested before proceeding to subsequent agents.

## Detailed N8N Workflow Configuration

### Workflow Structure Overview
```
[Email Trigger] → [Email Classifier AI] → [Classification Router] → [Data Storage]
                                      ↓
[Attachment Handler] → [File Storage] → [Processing Queue]
                                      ↓
[Error Handler] → [Exception Queue] → [Human Review Dashboard]
```

### Node-by-Node Configuration

#### Node 1: Gmail Trigger Configuration
**Node Name**: "PO Email Monitor"
**Node Type**: Gmail Trigger

**Detailed Settings**:
```json
{
  "authentication": "oAuth2",
  "resource": "message",
  "operation": "getAll",
  "returnAll": false,
  "limit": 50,
  "filters": {
    "q": "subject:(PO OR \"Purchase Order\" OR Order OR Amendment) AND has:attachment",
    "labelIds": ["INBOX"],
    "includeSpamTrash": false
  },
  "options": {
    "downloadAttachments": true,
    "dataPropertyAttachmentsPrefixName": "attachment_",
    "format": "full"
  },
  "pollTimes": {
    "item": [
      {
        "mode": "everyMinute",
        "minute": 5
      }
    ]
  }
}
```

#### Node 2: Email Preprocessing
**Node Name**: "Email Data Preprocessor"
**Node Type**: Code Node

**JavaScript Code**:
```javascript
// Preprocess email data for AI classification
const items = $input.all();
const processedItems = [];

for (const item of items) {
  const emailData = {
    messageId: item.json.id,
    threadId: item.json.threadId,
    sender: item.json.payload.headers.find(h => h.name === 'From')?.value || '',
    senderName: item.json.payload.headers.find(h => h.name === 'From')?.value?.match(/^([^<]+)/)?.[1]?.trim() || '',
    recipient: item.json.payload.headers.find(h => h.name === 'To')?.value || '',
    subject: item.json.payload.headers.find(h => h.name === 'Subject')?.value || '',
    receivedDateTime: new Date(parseInt(item.json.internalDate)).toISOString(),
    body: extractEmailBody(item.json.payload),
    attachments: extractAttachments(item.json.payload),
    priority: determinePriority(item.json.payload.headers)
  };

  processedItems.push({ json: emailData });
}

function extractEmailBody(payload) {
  if (payload.body && payload.body.data) {
    return Buffer.from(payload.body.data, 'base64').toString('utf-8');
  }

  if (payload.parts) {
    for (const part of payload.parts) {
      if (part.mimeType === 'text/plain' && part.body.data) {
        return Buffer.from(part.body.data, 'base64').toString('utf-8');
      }
    }
  }

  return '';
}

function extractAttachments(payload) {
  const attachments = [];

  if (payload.parts) {
    for (const part of payload.parts) {
      if (part.filename && part.body.attachmentId) {
        attachments.push({
          filename: part.filename,
          mimeType: part.mimeType,
          size: part.body.size,
          attachmentId: part.body.attachmentId
        });
      }
    }
  }

  return attachments;
}

function determinePriority(headers) {
  const priority = headers.find(h => h.name === 'X-Priority')?.value;
  const importance = headers.find(h => h.name === 'Importance')?.value;

  if (priority === '1' || importance === 'high') return 'High';
  if (priority === '5' || importance === 'low') return 'Low';
  return 'Normal';
}

return processedItems;
```

#### Node 3: AI Classification Agent
**Node Name**: "Email Classification AI"
**Node Type**: OpenAI Node

**Configuration**:
```json
{
  "resource": "chat",
  "operation": "create",
  "model": "gpt-4-turbo-preview",
  "messages": {
    "values": [
      {
        "role": "system",
        "content": "You are an expert email classifier for purchase order processing. Analyze emails and classify them accurately with high confidence scores. Always respond with valid JSON only."
      },
      {
        "role": "user",
        "content": "=Analyze this email and classify it:\n\nSubject: {{$json.subject}}\nFrom: {{$json.sender}} ({{$json.senderName}})\nBody: {{$json.body}}\nAttachments: {{$json.attachments.length}} files\nPriority: {{$json.priority}}\n\nReturn classification in this exact JSON format:\n{\n  \"messageId\": \"{{$json.messageId}}\",\n  \"classification\": {\n    \"emailType\": \"PURCHASE_ORDER|PO_AMENDMENT|PRICE_INQUIRY|ORDER_CANCELLATION|GENERAL_INQUIRY\",\n    \"confidence\": 0.XX,\n    \"urgencyLevel\": \"HIGH|MEDIUM|LOW\",\n    \"hasAttachments\": {{$json.attachments.length > 0}},\n    \"attachmentTypes\": {{JSON.stringify($json.attachments.map(a => a.mimeType))}},\n    \"requiredAction\": \"EXTRACT_PO_DATA|PROCESS_AMENDMENT|FORWARD_TO_SALES|PROCESS_CANCELLATION|ROUTE_TO_SUPPORT\",\n    \"extractedInfo\": {\n      \"poNumber\": \"extracted PO number or null\",\n      \"customerCompany\": \"extracted company name\",\n      \"deliveryRequirement\": \"extracted delivery info or null\",\n      \"specialInstructions\": \"extracted special instructions or null\"\n    },\n    \"sentimentAnalysis\": {\n      \"overallSentiment\": \"URGENT_PROFESSIONAL|NEUTRAL|FRUSTRATED|SATISFIED\",\n      \"urgencyIndicators\": [\"list of urgency keywords found\"],\n      \"satisfactionLevel\": \"HIGH|MEDIUM|LOW\",\n      \"emotionalTone\": \"BUSINESS_CRITICAL|ROUTINE|CONCERNED|APPRECIATIVE\"\n    },\n    \"relationshipContext\": {\n      \"customerType\": \"NEW_CUSTOMER|RETURNING_CUSTOMER\",\n      \"relationshipLength\": \"estimated relationship duration\",\n      \"previousInteractions\": 0,\n      \"escalationHistory\": \"NONE|LOW|MEDIUM|HIGH\"\n    },\n    \"priorityScoring\": {\n      \"customerValue\": \"LOW|MEDIUM|HIGH\",\n      \"orderSizeIndicator\": \"SMALL|MEDIUM|LARGE\",\n      \"strategicImportance\": \"LOW|MEDIUM|HIGH|CRITICAL\",\n      \"overallPriority\": 1-10,\n      \"priorityFactors\": [\"list of factors affecting priority\"]\n    }\n  },\n  \"nextAction\": \"determined next action\",\n  \"processingPriority\": 1-10\n}"
      }
    ]
  },
  "options": {
    "temperature": 0.1,
    "maxTokens": 2000,
    "topP": 1,
    "frequencyPenalty": 0,
    "presencePenalty": 0
  }
}
```

#### Node 4: JSON Parser
**Node Name**: "Parse AI Response"
**Node Type**: Code Node

**JavaScript Code**:
```javascript
// Parse and validate AI response
const items = $input.all();
const parsedItems = [];

for (const item of items) {
  try {
    const aiResponse = item.json.choices[0].message.content;

    // Clean the response to ensure valid JSON
    const cleanedResponse = aiResponse.replace(/```json\n?|\n?```/g, '').trim();

    const parsedResponse = JSON.parse(cleanedResponse);

    // Validate required fields
    if (!parsedResponse.messageId || !parsedResponse.classification) {
      throw new Error('Invalid AI response structure');
    }

    // Add processing metadata
    parsedResponse.processingMetadata = {
      processedAt: new Date().toISOString(),
      aiModel: 'gpt-4-turbo-preview',
      processingTime: Date.now() - item.json.created,
      nodeVersion: '1.0.0'
    };

    parsedItems.push({ json: parsedResponse });

  } catch (error) {
    // Handle parsing errors
    const errorItem = {
      error: {
        type: 'JSON_PARSING_ERROR',
        message: error.message,
        originalResponse: item.json.choices[0].message.content,
        timestamp: new Date().toISOString()
      },
      originalData: item.json
    };

    parsedItems.push({ json: errorItem });
  }
}

return parsedItems;
```

#### Node 5: Classification Router
**Node Name**: "Route Based on Classification"
**Node Type**: Switch Node

**Routing Rules**:
```json
{
  "rules": {
    "values": [
      {
        "conditions": {
          "values": [
            {
              "leftValue": "={{$json.classification.emailType}}",
              "rightValue": "PURCHASE_ORDER",
              "operation": "equal"
            },
            {
              "leftValue": "={{$json.classification.confidence}}",
              "rightValue": 0.85,
              "operation": "largerEqual"
            }
          ],
          "combinator": "and"
        },
        "renameOutput": "Purchase Order Processing"
      },
      {
        "conditions": {
          "values": [
            {
              "leftValue": "={{$json.classification.emailType}}",
              "rightValue": "PO_AMENDMENT",
              "operation": "equal"
            },
            {
              "leftValue": "={{$json.classification.confidence}}",
              "rightValue": 0.8,
              "operation": "largerEqual"
            }
          ],
          "combinator": "and"
        },
        "renameOutput": "Amendment Processing"
      },
      {
        "conditions": {
          "values": [
            {
              "leftValue": "={{$json.classification.emailType}}",
              "rightValue": "PRICE_INQUIRY",
              "operation": "equal"
            }
          ]
        },
        "renameOutput": "Sales Team Routing"
      },
      {
        "conditions": {
          "values": [
            {
              "leftValue": "={{$json.classification.confidence}}",
              "rightValue": 0.7,
              "operation": "smaller"
            }
          ]
        },
        "renameOutput": "Human Review Required"
      }
    ]
  },
  "fallbackOutput": "Default Processing"
}
```

## Advanced Configuration Options

### Custom LLM Prompts for Different Industries

#### Manufacturing Industry Prompt:
```
You are analyzing emails for a manufacturing supply chain. Focus on:
- Production schedules and lead times
- Quality specifications and certifications
- Bulk order quantities and volume discounts
- Technical specifications and compliance requirements
```

#### Office Supplies Industry Prompt:
```
You are analyzing emails for office supply orders. Focus on:
- Delivery locations (offices, warehouses, remote locations)
- Seasonal ordering patterns
- Budget approval processes
- Recurring order schedules
```

### Performance Optimization Settings

#### High-Volume Processing Configuration:
```json
{
  "batchProcessing": {
    "enabled": true,
    "batchSize": 10,
    "processingInterval": "30s"
  },
  "caching": {
    "enabled": true,
    "ttl": 3600,
    "cacheKey": "email_classification_{{messageId}}"
  },
  "rateLimiting": {
    "requestsPerMinute": 60,
    "burstLimit": 10
  }
}
```

### Integration Webhooks

#### Slack Notification for High-Priority Orders:
```javascript
// Webhook configuration for Slack notifications
if ($json.classification.priorityScoring.overallPriority >= 8) {
  const slackMessage = {
    channel: "#order-processing",
    username: "Order Processing Bot",
    text: `🚨 High Priority Order Detected`,
    attachments: [
      {
        color: "danger",
        fields: [
          {
            title: "Customer",
            value: $json.classification.extractedInfo.customerCompany,
            short: true
          },
          {
            title: "PO Number",
            value: $json.classification.extractedInfo.poNumber,
            short: true
          },
          {
            title: "Priority Score",
            value: $json.classification.priorityScoring.overallPriority,
            short: true
          },
          {
            title: "Urgency Level",
            value: $json.classification.urgencyLevel,
            short: true
          }
        ]
      }
    ]
  };

  return { json: slackMessage };
}
```

## Troubleshooting Guide

### Common Issues and Solutions

#### Issue 1: Low Classification Confidence
**Symptoms**: Confidence scores consistently below 0.7
**Solutions**:
- Review and refine LLM prompts
- Add more specific examples to training data
- Adjust temperature settings (lower for more consistent results)
- Implement additional preprocessing steps

#### Issue 2: Attachment Processing Failures
**Symptoms**: Attachments not being processed correctly
**Solutions**:
- Verify file size limits
- Check supported MIME types
- Implement retry logic for failed downloads
- Add fallback processing methods

#### Issue 3: Database Connection Issues
**Symptoms**: Data not being stored properly
**Solutions**:
- Check database connection settings
- Verify table schema matches data structure
- Implement connection pooling
- Add database health checks

#### Issue 4: API Rate Limiting
**Symptoms**: LLM API calls being throttled
**Solutions**:
- Implement exponential backoff
- Add request queuing
- Consider using multiple API keys
- Optimize prompt length to reduce token usage

### Monitoring and Alerting

#### Key Performance Indicators (KPIs):
```javascript
// KPI calculation code
const kpis = {
  classificationAccuracy: calculateAccuracy($json.results),
  averageProcessingTime: calculateAverageTime($json.processingTimes),
  errorRate: calculateErrorRate($json.errors, $json.total),
  humanInterventionRate: calculateHumanRate($json.humanReviews, $json.total),
  customerSatisfactionScore: calculateSatisfaction($json.feedback)
};

function calculateAccuracy(results) {
  const correct = results.filter(r => r.humanValidated && r.aiCorrect).length;
  return (correct / results.length) * 100;
}

function calculateAverageTime(times) {
  return times.reduce((sum, time) => sum + time, 0) / times.length;
}

function calculateErrorRate(errors, total) {
  return (errors / total) * 100;
}
```

This comprehensive implementation guide provides everything needed to successfully deploy the Document Intelligence Agent in N8N, with detailed configurations, error handling, and optimization strategies.
