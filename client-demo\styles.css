/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #e2e8f0;
    line-height: 1.5;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Header - Completely Revamped */
.demo-header {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    color: #f1f5f9;
    padding: 20px 30px;
    position: relative;
    overflow: hidden;
    margin-bottom: 0;
    border-bottom: 1px solid #334155;
}

.demo-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.header-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 30px;
    align-items: center;
}

.header-main {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    justify-content: space-between;
}

.header-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #10b981 0%, #**********%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.3);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
}

.header-logo {
    display: flex;
    align-items: center;
    justify-content: center;
}

.company-logo {
    height: 50px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
    filter: brightness(1.1) contrast(1.1);
    transition: all 0.3s ease;
}

.company-logo:hover {
    filter: brightness(1.2) contrast(1.2);
    transform: scale(1.02);
}

.header-title h1 {
    font-size: clamp(24px, 3vw, 32px);
    font-weight: 700;
    margin: 0;
    line-height: 1.1;
}

.header-subtitle {
    font-size: clamp(16px, 2vw, 18px);
    opacity: 0.9;
    font-weight: 400;
    margin: 0;
    line-height: 1.3;
}

.highlight-time {
    color: #fbbf24;
    font-weight: 700;
    text-shadow: 0 0 15px rgba(251, 191, 36, 0.5);
}

.header-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 16px;
}

.metric-card {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-radius: 12px;
    padding: 12px 16px;
    text-align: center;
    transition: all 0.3s ease;
}

.metric-card:hover {
    background: rgba(30, 41, 59, 0.9);
    transform: translateY(-2px);
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.1);
}

.metric-icon {
    font-size: 18px;
    margin-bottom: 4px;
    display: block;
}

.metric-value {
    font-size: clamp(16px, 2vw, 20px);
    font-weight: 700;
    margin-bottom: 2px;
    display: block;
    line-height: 1;
}

.metric-label {
    font-size: clamp(10px, 1.2vw, 12px);
    opacity: 0.8;
    font-weight: 500;
    line-height: 1.2;
}

.header-cta {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: flex-end;
    text-align: right;
}

.roi-display {
    background: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 16px;
    padding: 16px 20px;
    min-width: 180px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.1);
    margin-top: 66px;

}

.roi-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.roi-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #0f172a;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.roi-value {
    font-size: clamp(24px, 3vw, 32px);
    font-weight: 800;
    margin-bottom: 4px;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.roi-description {
    font-size: clamp(11px, 1.3vw, 13px);
    opacity: 0.85;
    line-height: 1.3;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    opacity: 0.8;
    margin-top: 8px;
}

.pulse-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

@keyframes countUp {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Main Container */
.demo-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Comparison Section */
.comparison-section {
    margin-bottom: 60px;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 0 15px;
}

.section-header h2 {
    font-size: clamp(28px, 5vw, 32px);
    font-weight: 700;
    color: #f1f5f9;
    margin-bottom: 8px;
    word-wrap: break-word;
}

.section-header p {
    font-size: clamp(16px, 2.5vw, 18px);
    color: #cbd5e1;
    word-wrap: break-word;
}

.comparison-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    margin-bottom: 40px;
    align-items: stretch;
}

.comparison-card {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
    word-wrap: break-word;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.comparison-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    border-color: rgba(16, 185, 129, 0.3);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 10px;
    flex-wrap: wrap;
}

.card-header h3 {
    font-size: clamp(18px, 3vw, 20px);
    font-weight: 700;
    word-wrap: break-word;
    flex: 1;
    min-width: 200px;
    color: #f1f5f9;
}

.before .card-header h3 {
    color: #ef4444;
}

.after .card-header h3 {
    color: #10b981;
}

.inefficiency-badge, .efficiency-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    letter-spacing: 0.3px;
    white-space: nowrap;
    flex-shrink: 0;
}

.inefficiency-badge {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.efficiency-badge {
    background: rgba(16, 185, 129, 0.2);
    color: #6ee7b7;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.process-time {
    font-size: clamp(28px, 5vw, 32px);
    font-weight: 700;
    margin-bottom: 16px;
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    word-wrap: break-word;
}

.before .process-time {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.after .process-time {
    background: rgba(16, 185, 129, 0.2);
    color: #6ee7b7;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.process-breakdown {
    margin-bottom: 16px;
}

.breakdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 6px 0;
    border-bottom: 1px solid rgba(51, 65, 85, 0.5);
    font-size: 13px;
    word-wrap: break-word;
    color: #cbd5e1;
}

.breakdown-item:last-child {
    border-bottom: none;
}

.step-time {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 40px;
    text-align: center;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.step-desc {
    flex: 1;
    word-wrap: break-word;
}

.cost-breakdown {
    margin-bottom: 16px;
}

.cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
    font-size: 13px;
    word-wrap: break-word;
}

.cost-label {
    color: #94a3b8;
    font-weight: 500;
}

.cost-value {
    font-weight: 600;
    color: #f1f5f9;
}

.cost-total {
    font-size: 16px;
    font-weight: 700;
    padding: 8px 12px;
    border-radius: 6px;
    text-align: center;
    margin-top: 8px;
}

.before .cost-total {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.after .cost-total {
    background: rgba(16, 185, 129, 0.2);
    color: #6ee7b7;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.problems, .benefits {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.problem-item, .benefit-item {
    font-size: 12px;
    padding: 4px 0;
    word-wrap: break-word;
}

.problem-item {
    color: #fca5a5;
}

.benefit-item {
    color: #6ee7b7;
}

.transformation-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    padding: 20px 10px;
}

.arrow-container {
    display: flex;
    align-items: center;
    gap: 5px;
}

.arrow-line {
    width: 40px;
    height: 2px;
    background: #3b82f6;
    display: none;
}

.arrow-head {
    font-size: clamp(32px, 5vw, 40px);
    color: #3b82f6;
    font-weight: bold;
}

.ai-transformation-badge {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 12px 16px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.badge-text {
    font-size: 11px;
    font-weight: 700;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 8px;
}

.transformation-stats {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.transform-stat {
    font-size: 10px;
    background: rgba(255, 255, 255, 0.2);
    padding: 3px 8px;
    border-radius: 8px;
    white-space: nowrap;
}

.savings-highlight {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.savings-card {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.savings-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    border-color: rgba(16, 185, 129, 0.3);
}

.savings-icon {
    font-size: 32px;
    flex-shrink: 0;
}

.savings-content {
    flex: 1;
}

.savings-title {
    font-size: 14px;
    font-weight: 600;
    color: #94a3b8;
    margin-bottom: 6px;
    word-wrap: break-word;
}

.savings-amount {
    font-size: clamp(18px, 3vw, 20px);
    font-weight: 700;
    color: #f1f5f9;
    word-wrap: break-word;
}

.savings-percent {
    font-size: 12px;
    color: #6ee7b7;
    font-weight: 500;
}

/* Flow Section */
.flow-section {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 16px;
    padding: 30px 25px;
    margin-bottom: 50px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.flow-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 0 15px;
}

.flow-header h2 {
    font-size: clamp(24px, 4vw, 28px);
    margin-bottom: 12px;
    color: #f1f5f9;
    word-wrap: break-word;
}

.flow-header p {
    font-size: clamp(14px, 2vw, 16px);
    color: #cbd5e1;
    margin-bottom: 20px;
    word-wrap: break-word;
}

.demo-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.demo-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.demo-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.5);
}

.demo-btn.primary {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    border-color: rgba(16, 185, 129, 0.3);
}

.demo-btn.secondary {
    background: linear-gradient(135deg, #64748b, #475569);
    box-shadow: 0 4px 15px rgba(100, 116, 139, 0.4);
    border-color: rgba(100, 116, 139, 0.3);
}

.demo-info {
    margin-top: 10px;
}

.info-text {
    font-size: 12px;
    color: #94a3b8;
    font-style: italic;
}

/* Progress Container */
.progress-container {
    margin-bottom: 30px;
    padding: 0 15px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(51, 65, 85, 0.5);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #10b981);
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #94a3b8;
    flex-wrap: wrap;
    gap: 10px;
}

/* Flow Steps */
.flow-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 30px;
}

.flow-step {
    display: grid;
    grid-template-columns: 60px 1fr 50px;
    gap: 15px;
    align-items: flex-start;
    background: rgba(30, 41, 59, 0.6);
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid rgba(51, 65, 85, 0.5);
    transition: all 0.5s ease;
    opacity: 0.6;
    word-wrap: break-word;
    border: 1px solid rgba(51, 65, 85, 0.3);
}

.flow-step.active {
    opacity: 1;
    border-left-color: #3b82f6;
    background: rgba(30, 41, 59, 0.9);
    transform: scale(1.01);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
}

.flow-step.completed {
    opacity: 1;
    border-left-color: #10b981;
    background: rgba(30, 41, 59, 0.9);
    border-color: rgba(16, 185, 129, 0.3);
}

.step-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-icon {
    font-size: clamp(24px, 4vw, 28px);
    text-align: center;
    background: linear-gradient(135deg, #1e293b, #334155);
    color: #f1f5f9;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    flex-shrink: 0;
    border: 2px solid rgba(51, 65, 85, 0.5);
}

.step-content {
    min-width: 0;
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 6px;
    gap: 10px;
    flex-wrap: wrap;
}

.step-content h4 {
    font-size: clamp(16px, 2.5vw, 18px);
    font-weight: 600;
    color: #f1f5f9;
    word-wrap: break-word;
    flex: 1;
    min-width: 150px;
}

.step-timing {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    white-space: nowrap;
    flex-shrink: 0;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.step-description {
    color: #cbd5e1;
    margin-bottom: 12px;
    font-size: 13px;
    word-wrap: break-word;
    line-height: 1.4;
}

.step-details {
    background: rgba(15, 23, 42, 0.8);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    word-wrap: break-word;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.step-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-align: center;
}

.status-indicator {
    font-size: 20px;
}

.status-text {
    font-size: 10px;
    color: #94a3b8;
    font-weight: 500;
}

/* Step Detail Styles */
.email-preview {
    font-size: 12px;
}

.email-window {
    background: rgba(15, 23, 42, 0.9);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.email-toolbar {
    background: rgba(51, 65, 85, 0.8);
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.email-buttons {
    display: flex;
    gap: 4px;
}

.btn-red, .btn-yellow, .btn-green {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.btn-red { background: #ff5f56; }
.btn-yellow { background: #ffbd2e; }
.btn-green { background: #27ca3f; }

.email-title {
    font-size: 11px;
    font-weight: 600;
    color: #cbd5e1;
}

.email-content {
    padding: 12px;
}

.email-header {
    margin-bottom: 10px;
    line-height: 1.3;
}

.email-from, .email-subject {
    margin-bottom: 4px;
    font-size: 11px;
    word-wrap: break-word;
    color: #e2e8f0;
}

.priority-badge {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 9px;
    font-weight: 700;
    display: inline-block;
    margin-top: 4px;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.email-attachment {
    background: rgba(51, 65, 85, 0.5);
    padding: 8px 10px;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    border: 1px solid rgba(51, 65, 85, 0.7);
}

.attachment-icon {
    font-size: 14px;
}

.attachment-info {
    flex: 1;
}

.attachment-name {
    font-size: 11px;
    font-weight: 600;
    color: #f1f5f9;
}

.attachment-size {
    font-size: 10px;
    color: #94a3b8;
}

.ai-analysis, .extracted-data, .validation-results, .order-summary {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.analysis-item, .data-item, .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 4px 0;
    gap: 10px;
    word-wrap: break-word;
}

.label {
    font-weight: 500;
    color: #94a3b8;
    font-size: 12px;
    flex-shrink: 0;
    min-width: 80px;
}

.value {
    font-weight: 600;
    color: #f1f5f9;
    font-size: 12px;
    text-align: right;
    word-wrap: break-word;
    flex: 1;
}

.value.highlight {
    color: #3b82f6;
    font-size: 13px;
}

.value.urgent {
    color: #fca5a5;
}

.value.success {
    color: #6ee7b7;
}

.validation-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
}

.validation-check {
    font-size: 14px;
    flex-shrink: 0;
}

/* Results Summary */
.results-summary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-radius: 12px;
    padding: 25px 20px;
    text-align: center;
    display: none;
    margin-top: 20px;
    border: 1px solid rgba(16, 185, 129, 0.3);
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.2);
}

.results-summary.show {
    display: block;
    animation: slideUp 0.5s ease-out;
}

.results-summary h3 {
    font-size: clamp(20px, 3vw, 22px);
    margin-bottom: 16px;
    word-wrap: break-word;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.stat {
    text-align: center;
    padding: 10px 5px;
}

.stat-number {
    font-size: clamp(20px, 4vw, 24px);
    font-weight: 700;
    margin-bottom: 4px;
    word-wrap: break-word;
}

.stat-label {
    font-size: clamp(11px, 1.5vw, 12px);
    opacity: 0.9;
    word-wrap: break-word;
}

/* Impact Section */
.impact-section {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 16px;
    padding: 30px 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    margin-bottom: 30px;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.impact-section h2 {
    text-align: center;
    font-size: clamp(24px, 4vw, 28px);
    margin-bottom: 25px;
    color: #f1f5f9;
    word-wrap: break-word;
}

.impact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.impact-card {
    text-align: center;
    padding: 20px 15px;
    border-radius: 12px;
    background: rgba(15, 23, 42, 0.8);
    transition: transform 0.3s ease;
    word-wrap: break-word;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.impact-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    border-color: rgba(16, 185, 129, 0.3);
}

.impact-icon {
    font-size: clamp(36px, 6vw, 42px);
    margin-bottom: 12px;
}

.impact-title {
    font-size: clamp(14px, 2vw, 16px);
    font-weight: 600;
    margin-bottom: 8px;
    color: #94a3b8;
    word-wrap: break-word;
}

.impact-value {
    font-size: clamp(20px, 3vw, 22px);
    font-weight: 700;
    margin-bottom: 4px;
    color: #3b82f6;
    word-wrap: break-word;
}

.impact-desc {
    font-size: clamp(12px, 1.5vw, 13px);
    color: #cbd5e1;
    word-wrap: break-word;
}

/* Animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .comparison-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .transformation-arrow {
        order: 2;
        padding: 15px;
    }

    .arrow-head {
        transform: rotate(90deg);
    }

    .comparison-card.before {
        order: 1;
    }

    .comparison-card.after {
        order: 3;
    }

    .flow-step {
        grid-template-columns: 50px 1fr 40px;
        gap: 12px;
        padding: 16px;
    }

    .step-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
}

@media (max-width: 768px) {
    .demo-header {
        flex-direction: column;
        text-align: center;
        padding: 15px 20px;
        gap: 15px;
    }

    .header-content {
        min-width: auto;
    }

    .roi-highlight {
        align-items: center;
        width: 100%;
    }

    .roi-badge {
        min-width: auto;
        width: 100%;
        max-width: 300px;
    }

    .header-stats {
        justify-content: center;
        gap: 10px;
    }

    .demo-container {
        padding: 0 15px;
    }

    .flow-section, .impact-section {
        padding: 20px 15px;
    }

    .comparison-card {
        padding: 20px;
    }

    .flow-step {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: center;
    }

    .step-status {
        order: -1;
        margin-bottom: 10px;
    }

    .step-icon-container {
        order: -1;
        margin-bottom: 10px;
    }

    .demo-controls {
        flex-direction: column;
        gap: 10px;
    }

    .demo-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-stats {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .savings-highlight {
        grid-template-columns: 1fr;
    }

    .savings-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .impact-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
}

/* Enhanced Step Detail Styles */
.extraction-header, .analysis-header, .validation-header, .completion-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(51, 65, 85, 0.5);
    flex-wrap: wrap;
}

/* Typewriter and Streaming Text Effects */
.streaming-text-container {
    margin-top: 15px;
    padding: 15px;
    background: rgba(15, 23, 42, 0.8);
    border-radius: 8px;
    border-left: 3px solid #3b82f6;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #e2e8f0;
    max-height: 300px;
    overflow-y: auto;
    animation: fadeInUp 0.5s ease-out;
}

.streaming-text-container::-webkit-scrollbar {
    width: 6px;
}

.streaming-text-container::-webkit-scrollbar-track {
    background: rgba(51, 65, 85, 0.3);
    border-radius: 3px;
}

.streaming-text-container::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.6);
    border-radius: 3px;
}

.streaming-text-container::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.8);
}

.typewriter-cursor::after {
    content: '|';
    animation: blink 1s infinite;
    color: #3b82f6;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Sub-steps Container Styles */
.sub-steps-container {
    margin-top: 15px;
    padding: 12px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(59, 130, 246, 0.3);
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced Progress Indicators */
.progress-text {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #4a5568;
    margin-top: 8px;
}

.progress-text #progressStatus {
    font-weight: 500;
    color: #e2e8f0;
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.progress-text #progressTime {
    font-weight: 600;
    color: #3b82f6;
    font-size: 12px;
}

.extraction-icon, .analysis-icon, .validation-icon, .completion-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.extraction-title, .analysis-title, .validation-title, .completion-title {
    font-size: clamp(14px, 2vw, 15px);
    font-weight: 600;
    color: #f1f5f9;
    flex: 1;
    word-wrap: break-word;
    min-width: 120px;
}

.extraction-confidence, .confidence-score, .validation-score, .completion-badge {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    white-space: nowrap;
    flex-shrink: 0;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.analysis-grid, .validation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 15px;
}

.analysis-item, .validation-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: rgba(15, 23, 42, 0.8);
    border-radius: 6px;
    border-left: 3px solid rgba(51, 65, 85, 0.5);
    word-wrap: break-word;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.analysis-icon, .validation-check {
    font-size: 14px;
    flex-shrink: 0;
}

.analysis-content, .validation-content {
    flex: 1;
    min-width: 0;
}

.validation-label {
    font-size: 12px;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 2px;
    word-wrap: break-word;
}

.validation-detail {
    font-size: 11px;
    color: #cbd5e1;
    word-wrap: break-word;
}

.data-item, .summary-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(51, 65, 85, 0.5);
    word-wrap: break-word;
}

.data-item:last-child, .summary-item:last-child {
    border-bottom: none;
}

.data-icon, .summary-icon {
    font-size: 14px;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.data-content, .summary-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    min-width: 0;
}

.extraction-progress, .completion-actions {
    margin-top: 12px;
}

.progress-item, .action-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
    font-size: 12px;
    word-wrap: break-word;
}

.progress-check, .action-check {
    font-size: 12px;
    flex-shrink: 0;
}

.validation-summary {
    text-align: center;
    margin-top: 15px;
}

.summary-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
}

.summary-badge.success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 3px 12px rgba(16, 185, 129, 0.4);
}

.badge-icon {
    font-size: 14px;
}

.ai-thinking {
    margin-top: 12px;
    text-align: center;
}

.thinking-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.thinking-dots {
    display: flex;
    gap: 3px;
}

.thinking-dots span {
    width: 5px;
    height: 5px;
    background: #3b82f6;
    border-radius: 50%;
    animation: thinkingDots 1.5s ease-in-out infinite;
}

.thinking-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

.thinking-text {
    font-size: 11px;
    color: #93c5fd;
    font-style: italic;
}

@keyframes thinkingDots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}

/* Additional utility classes for better text handling */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-wrap {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

/* Improved focus states for accessibility */
.demo-btn:focus,
.flow-step:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Better loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #3b82f6;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Advanced AI Features Styles */
.advanced-analysis {
    margin-top: 20px;
    padding: 15px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.analysis-section {
    margin-bottom: 15px;
}

.analysis-section:last-child {
    margin-bottom: 0;
}

.analysis-section h5 {
    font-size: 14px;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.pattern-grid, .market-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.pattern-item, .market-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(30, 41, 59, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(59, 130, 246, 0.3);
    font-size: 12px;
}

.pattern-icon, .market-icon {
    font-size: 14px;
    flex-shrink: 0;
}

.pattern-label, .market-label {
    font-weight: 500;
    color: #94a3b8;
}

.pattern-value, .market-value {
    font-weight: 600;
    margin-left: auto;
}

.pattern-value.stable, .market-value.competitive {
    color: #6ee7b7;
}

.pattern-value.safe {
    color: #10b981;
}

.pattern-value.normal {
    color: #93c5fd;
}

.market-value.strong {
    color: #6ee7b7;
}

/* Contract Terms Analysis */
.contract-analysis {
    margin-top: 20px;
    padding: 15px;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.contract-analysis h5 {
    font-size: 14px;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.contract-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 10px;
}

.contract-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    background: rgba(30, 41, 59, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(16, 185, 129, 0.3);
    font-size: 12px;
}

.contract-icon {
    font-size: 14px;
    flex-shrink: 0;
}

.contract-label {
    font-weight: 500;
    color: #94a3b8;
}

.contract-value {
    font-weight: 600;
    margin-left: auto;
}

.contract-value.critical {
    color: #fca5a5;
}

.contract-value.safe {
    color: #6ee7b7;
}

/* Supply Chain Optimization */
.optimization-section {
    margin-top: 20px;
    padding: 15px;
    background: rgba(245, 158, 11, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.optimization-section h5 {
    font-size: 14px;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.optimization-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.optimization-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    background: rgba(30, 41, 59, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(245, 158, 11, 0.3);
    font-size: 12px;
}

.opt-icon {
    font-size: 14px;
    flex-shrink: 0;
}

.opt-label {
    font-weight: 500;
    color: #94a3b8;
}

.opt-value {
    font-weight: 600;
    margin-left: auto;
    color: #f1f5f9;
}

.opt-value.recommended {
    color: #93c5fd;
}

.opt-value.eco {
    color: #6ee7b7;
}

.opt-value.savings {
    color: #fbbf24;
}

/* Margin Optimization */
.margin-section {
    margin-top: 20px;
    padding: 15px;
    background: rgba(139, 92, 246, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.margin-section h5 {
    font-size: 14px;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.margin-opportunities {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.opportunity-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 12px;
    background: rgba(30, 41, 59, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(139, 92, 246, 0.3);
    font-size: 12px;
}

.opp-icon {
    font-size: 14px;
    flex-shrink: 0;
    color: #c4b5fd;
}

.opp-text {
    font-weight: 500;
    color: #cbd5e1;
    line-height: 1.4;
}

/* Human-in-the-Loop Control Points */
.control-points-section {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 16px;
    padding: 30px;
    margin: 40px 0;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.control-tabs {
    display: flex;
    gap: 2px;
    background: rgba(15, 23, 42, 0.8);
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 30px;
    overflow-x: auto;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.control-tab {
    flex: 1;
    min-width: 150px;
    padding: 12px 20px;
    background: transparent;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 14px;
}

.control-tab:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
}

.control-tab.active {
    background: #3b82f6;
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.control-content {
    display: none;
}

.control-content.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

/* Dashboard Components */
.dashboard-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(51, 65, 85, 0.5);
}

.dashboard-icon {
    font-size: 24px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 12px;
    color: white;
}

.dashboard-title {
    font-size: 20px;
    font-weight: 700;
    color: #f1f5f9;
    flex: 1;
}

.dashboard-badge {
    background: #e53e3e;
    color: white;
    padding: 3px 6px;
    border-radius: 16px;
    
    font-size: 12px;
    font-weight: 600;
}

.tabs-icon{
    margin-bottom: 10px;
}

/* Exception Queue Styles */
.exception-items {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.exception-item {
    background: rgba(15, 23, 42, 0.8);
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid #ef4444;
    transition: all 0.3s ease;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.exception-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(239, 68, 68, 0.3);
}

.exception-priority {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 12px;
}

.exception-priority.high {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.exception-priority.medium {
    background: rgba(245, 158, 11, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.exception-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.exception-id {
    font-weight: 700;
    color: #3b82f6;
    font-size: 14px;
}

.exception-type {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.exception-time {
    color: #94a3b8;
    font-size: 12px;
    margin-left: auto;
}

.exception-description {
    color: #cbd5e1;
    margin-bottom: 15px;
    line-height: 1.5;
    font-size: 14px;
}

.ai-recommendation {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
}

.rec-header {
    font-weight: 600;
    color: #6ee7b7;
    margin-bottom: 6px;
    font-size: 13px;
}

.rec-text {
    color: #cbd5e1;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 6px;
}

.rec-confidence {
    font-size: 11px;
    color: #94a3b8;
    font-style: italic;
}

.exception-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.action-btn.primary {
    background: #3b82f6;
    color: white;
}

.action-btn.primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: rgba(51, 65, 85, 0.8);
    color: #cbd5e1;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.action-btn.secondary:hover {
    background: rgba(71, 85, 105, 0.9);
}

.action-btn.tertiary {
    background: transparent;
    color: #3b82f6;
    border: 1px solid #3b82f6;
}

.action-btn.tertiary:hover {
    background: #3b82f6;
    color: white;
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Approval Workflow Styles */
.approval-item {
    background: #f7fafc;
    border-radius: 12px;
    padding: 25px;
    border-left: 4px solid #ed8936;
}

.approval-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.approval-type {
    font-size: 18px;
    font-weight: 700;
    color: #2d3748;
}

.approval-amount {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
}

.approval-urgency {
    background: #fed7d7;
    color: #c53030;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
}

.detail-section {
    margin-bottom: 25px;
}

.detail-section h4 {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 12px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.detail-label {
    font-weight: 500;
    color: #4a5568;
    font-size: 13px;
}

.detail-value {
    font-weight: 600;
    color: #2d3748;
    font-size: 13px;
}

.detail-value.highlight {
    color: #ed8936;
}

/* AI Risk Assessment */
.ai-risk-assessment {
    background: rgba(102, 126, 234, 0.05);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.ai-risk-assessment h4 {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
}

.risk-score {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 15px;
}

.score-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    position: relative;
}

.score-circle.medium-risk {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.score-number {
    font-size: 24px;
    line-height: 1;
}

.score-label {
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.risk-factors {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.risk-factor {
    font-size: 13px;
    padding: 6px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #0f172a;
}

.ai-recommendation-box {
    background: rgba(72, 187, 120, 0.05);
    border: 1px solid rgba(72, 187, 120, 0.2);
    border-radius: 8px;
    padding: 15px;
}

.ai-recommendation-box .rec-header {
    font-size: 14px;
    font-weight: 600;
    color: #38a169;
    margin-bottom: 10px;
}

.rec-conditions {
    margin-bottom: 12px;
}

.condition {
    font-size: 13px;
    color: #4a5568;
    margin-bottom: 4px;
    padding-left: 8px;
}

.rec-justification {
    font-size: 13px;
    color: #4a5568;
    line-height: 1.4;
}

.approval-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.action-btn.approve {
    background: #10b981;
    color: white;
}

.action-btn.approve:hover {
    background: #059669;
}

.action-btn.reject {
    background: #ef4444;
    color: white;
}

.action-btn.reject:hover {
    background: #dc2626;
}

.action-btn.modify {
    background: #f59e0b;
    color: white;
}

.action-btn.modify:hover {
    background: #d97706;
}

.action-btn.escalate {
    background: #8b5cf6;
    color: white;
}

.action-btn.escalate:hover {
    background: #7c3aed;
}

/* Regulation Monitoring Styles */
.regulation-alerts {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.alert-item {
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid;
    display: flex;
    gap: 15px;
    transition: all 0.3s ease;
}

.alert-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.alert-item.critical {
    background: #fed7d7;
    border-left-color: #e53e3e;
}

.alert-item.warning {
    background: #feebc8;
    border-left-color: #ed8936;
}

.alert-item.info {
    background: #bee3f8;
    border-left-color: #3182ce;
}

.alert-icon {
    font-size: 24px;
    flex-shrink: 0;
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.alert-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

.alert-date {
    background: rgba(0, 0, 0, 0.1);
    color: #4a5568;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    margin-left: auto;
}

.alert-description {
    color: #4a5568;
    margin-bottom: 10px;
    line-height: 1.5;
    font-size: 14px;
}

.alert-impact {
    margin-bottom: 15px;
    font-size: 13px;
    color: #2d3748;
}

.alert-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn.urgent {
    background: #e53e3e;
    color: white;
}

.action-btn.urgent:hover {
    background: #c53030;
}

/* Analytics Dashboard Styles */
.analytics-section {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 16px;
    padding: 30px;
    margin: 40px 0;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.analytics-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.analytics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
}

.analytics-card {
    background: rgba(15, 23, 42, 0.8);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(51, 65, 85, 0.5);
    transition: all 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(16, 185, 129, 0.3);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e2e8f0;
}

.card-icon {
    font-size: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    color: white;
}

.roi-card .card-icon {
    background: linear-gradient(135deg, #48bb78, #38a169);
}

.performance-card .card-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.optimization-card .card-icon {
    background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.learning-card .card-icon {
    background: linear-gradient(135deg, #805ad5, #6b46c1);
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #f1f5f9;
    flex: 1;
}

.card-period, .card-badge, .card-status {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

/* ROI Metrics */
.roi-metrics {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.roi-item {
    background: rgba(30, 41, 59, 0.8);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.roi-label {
    font-size: 13px;
    font-weight: 500;
    color: #94a3b8;
    margin-bottom: 6px;
}

.roi-value {
    font-size: 24px;
    font-weight: 700;
    color: #10b981;
    margin-bottom: 8px;
}

.roi-breakdown {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.breakdown-item {
    font-size: 11px;
    color: #cbd5e1;
    display: flex;
    justify-content: space-between;
}

.roi-summary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    border: 1px solid rgba(16, 185, 129, 0.3);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
}

.summary-label {
    font-size: 13px;
    margin-bottom: 6px;
    opacity: 0.9;
}

.summary-value {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 4px;
}

.summary-note {
    font-size: 11px;
    opacity: 0.8;
}

/* Performance Metrics */
.performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(30, 41, 59, 0.8);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.metric-icon {
    font-size: 18px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-radius: 6px;
    flex-shrink: 0;
}

.metric-content {
    flex: 1;
}

.metric-label {
    font-size: 12px;
    font-weight: 500;
    color: #94a3b8;
    margin-bottom: 2px;
}

.metric-value {
    font-size: 18px;
    font-weight: 700;
    color: #f1f5f9;
    margin-bottom: 2px;
}

.metric-change {
    font-size: 11px;
    font-weight: 600;
}

.metric-change.positive {
    color: #6ee7b7;
}

.metric-change.negative {
    color: #fca5a5;
}

/* Optimization Recommendations */
.optimization-card {
    grid-column: 1 / -1;
}

.optimization-recommendations {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.recommendation-item {
    background: rgba(30, 41, 59, 0.8);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(51, 65, 85, 0.5);
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.recommendation-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.recommendation-item.high-impact {
    border-left-color: #ef4444;
}

.recommendation-item.medium-impact {
    border-left-color: #f59e0b;
}

.recommendation-item.low-impact {
    border-left-color: #3b82f6;
}

.rec-priority {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 12px;
}

.high-impact .rec-priority {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.medium-impact .rec-priority {
    background: rgba(245, 158, 11, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.low-impact .rec-priority {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.rec-title {
    font-size: 16px;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 8px;
}

.rec-description {
    color: #cbd5e1;
    margin-bottom: 12px;
    line-height: 1.5;
    font-size: 14px;
}

.rec-impact {
    margin-bottom: 15px;
}

.impact-label {
    font-weight: 500;
    color: #94a3b8;
    font-size: 13px;
}

.impact-value {
    font-weight: 600;
    color: #6ee7b7;
    font-size: 13px;
}

.rec-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.rec-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.rec-btn.primary {
    background: #3b82f6;
    color: white;
}

.rec-btn.primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.rec-btn.secondary {
    background: rgba(51, 65, 85, 0.8);
    color: #cbd5e1;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.rec-btn.secondary:hover {
    background: rgba(71, 85, 105, 0.9);
}

.implementation-status {
    color: #6ee7b7;
    font-weight: 600;
    font-size: 12px;
}

/* Learning & Adaptation */
.learning-metrics {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.learning-section h4 {
    font-size: 14px;
    font-weight: 600;
    color: #f1f5f9;
    margin-bottom: 12px;
}

.improvement-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.improvement-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 12px;
    background: rgba(30, 41, 59, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(51, 65, 85, 0.5);
    font-size: 13px;
}

.improvement-icon {
    font-size: 16px;
    flex-shrink: 0;
    color: #6ee7b7;
}

.improvement-text {
    color: #cbd5e1;
    line-height: 1.4;
}

.training-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
}

.training-stat {
    background: rgba(30, 41, 59, 0.8);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid rgba(51, 65, 85, 0.5);
    text-align: center;
}

.stat-label {
    font-size: 11px;
    font-weight: 500;
    color: whitesmoke;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: #c4b5fd;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(30, 41, 59, 0.95);
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
    border-left: 4px solid;
    z-index: 10000;
    animation: slideIn 0.3s ease-out;
    max-width: 400px;
    border: 1px solid rgba(51, 65, 85, 0.5);
}

.notification-success {
    border-left-color: #10b981;
}

.notification-error {
    border-left-color: #ef4444;
}

.notification-warning {
    border-left-color: #f59e0b;
}

.notification-info {
    border-left-color: #3b82f6;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    gap: 15px;
}

.notification-message {
    font-size: 14px;
    color: #f1f5f9;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #94a3b8;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-close:hover {
    color: #f1f5f9;
}

/* Additional Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .demo-header {
        padding: 20px;
    }

    .header-content {
        grid-template-columns: 1fr;
        gap: 20px;
        text-align: center;
    }

    .header-title {
        justify-content: center;
    }

    .header-title h1 {
        font-size: clamp(20px, 5vw, 28px);
    }

    .header-subtitle {
        font-size: clamp(14px, 3vw, 16px);
    }

    .header-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .metric-card {
        padding: 10px 12px;
    }

    .metric-value {
        font-size: clamp(14px, 3vw, 18px);
    }

    .metric-label {
        font-size: clamp(9px, 2vw, 11px);
    }

    .header-cta {
        align-items: center;
        text-align: center;
    }

    .roi-display {
        min-width: auto;
        width: 100%;
        max-width: 250px;
        
    }

    .roi-value {
        font-size: clamp(20px, 5vw, 28px);
    }

    .roi-description {
        font-size: clamp(10px, 2.5vw, 12px);
    }

    .control-tabs {
        flex-direction: column;
    }

    .control-tab {
        min-width: auto;
        text-align: center;
    }

    .analytics-row {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .risk-score {
        flex-direction: column;
        text-align: center;
    }

    .exception-header,
    .alert-header,
    .approval-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .exception-time,
    .alert-date {
        margin-left: 0;
    }

    .notification {
        left: 20px;
        right: 20px;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .demo-header {
        padding: 15px;
    }

    .header-title {
        flex-direction: column;
        gap: 8px;
    }

    .header-icon {
        width: 35px;
        height: 35px;
        font-size: 18px;
    }

    .header-title h1 {
        font-size: clamp(18px, 6vw, 24px);
    }

    .header-subtitle {
        font-size: clamp(12px, 4vw, 14px);
    }

    .header-metrics {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .metric-card {
        padding: 8px 10px;
    }

    .roi-display {
        padding: 12px 16px;
        max-width: 200px;
    }

    .roi-value {
        font-size: clamp(18px, 6vw, 24px);
    }

    .roi-description {
        font-size: clamp(9px, 3vw, 11px);
    }
}

/* Credit Assessment Styles */
.credit-assessment {
    background: rgba(15, 23, 42, 0.8);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.credit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.credit-icon {
    color: #10b981;
    margin-right: 12px;
}

.credit-title {
    font-size: 18px;
    font-weight: 600;
    color: #e2e8f0;
    flex: 1;
}

.credit-score {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.credit-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.metric-icon {
    color: #3b82f6;
}

.metric-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.metric-label {
    font-size: 12px;
    color: #94a3b8;
    font-weight: 500;
}

.metric-value {
    font-size: 14px;
    font-weight: 600;
}

.metric-value.excellent {
    color: #10b981;
}

.metric-value.good {
    color: #3b82f6;
}

.metric-value.low-risk {
    color: #10b981;
}

.metric-value.approved {
    color: #10b981;
}

.business-value-section {
    margin: 20px 0;
    padding: 15px;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.business-value-section h5 {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #10b981;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
}

.value-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.value-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.value-icon {
    font-size: 16px;
}

.value-label {
    color: #94a3b8;
    font-weight: 500;
}

.value-metric {
    color: #e2e8f0;
    font-weight: 600;
}

.credit-decision {
    margin-top: 20px;
    text-align: center;
}

.decision-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
}

.decision-badge.approved {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.decision-icon {
    font-size: 16px;
}

/* Fulfillment Coordination Styles */
.fulfillment-coordination {
    background: rgba(15, 23, 42, 0.8);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.fulfillment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.fulfillment-icon {
    color: #f59e0b;
    margin-right: 12px;
}

.fulfillment-title {
    font-size: 18px;
    font-weight: 600;
    color: #e2e8f0;
    flex: 1;
}

.fulfillment-status {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.coordination-sections {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
}

.coordination-section h5 {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #3b82f6;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
}

.warehouse-grid,
.logistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.warehouse-item,
.logistics-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    font-size: 13px;
}

.warehouse-icon,
.logistics-icon {
    font-size: 16px;
}

.warehouse-label,
.logistics-label {
    color: #94a3b8;
    font-weight: 500;
}

.warehouse-value,
.logistics-value {
    color: #e2e8f0;
    font-weight: 600;
}

.warehouse-value.optimal,
.logistics-value.selected {
    color: #10b981;
}

.warehouse-value.available,
.logistics-value.confirmed {
    color: #3b82f6;
}

.warehouse-value.scheduled,
.logistics-value.eco {
    color: #10b981;
}

.roi-metrics-section {
    margin: 20px 0;
    padding: 15px;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.roi-metrics-section h5 {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #10b981;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
}

.roi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.roi-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.roi-icon {
    font-size: 16px;
}

.roi-label {
    color: #94a3b8;
    font-weight: 500;
}

.roi-metric {
    color: #e2e8f0;
    font-weight: 600;
}

.tracking-setup {
    margin-top: 20px;
    text-align: center;
}

.tracking-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    font-size: 14px;
    color: #3b82f6;
    font-weight: 500;
}

.tracking-icon {
    color: #10b981;
}

/* Responsive adjustments for new sections */
@media (max-width: 768px) {
    .credit-metrics,
    .warehouse-grid,
    .logistics-grid,
    .value-grid,
    .roi-grid {
        grid-template-columns: 1fr;
    }

    .credit-header,
    .fulfillment-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .coordination-sections {
        gap: 15px;
    }
}
